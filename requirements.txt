# Flask and Web Framework
Flask==3.0.0
Flask-RESTful==0.3.10
Flask-CORS==4.0.0
Flask-JWT-Extended==4.6.0
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5

# Data and Analytics
pandas==2.1.4
numpy==1.26.2
yfinance==0.2.28
alpha-vantage==2.3.1
requests==2.31.0
python-dotenv==1.0.0

# Technical Analysis
# Alternative technical analysis libraries (TA-Lib replacement)
# TA-Lib requires system dependencies that may not be available
# Using pandas-ta and ta as alternatives for technical indicators
pandas-ta==0.3.14b0
ta==0.10.2

# Database
SQLAlchemy==2.0.23
psycopg2-binary==2.9.9

# Async and Scheduling
APScheduler==3.10.4
celery==5.3.4
redis==5.0.1

# Utilities
marshmallow==3.20.2
python-dateutil==2.8.2
pytz==2023.3.post1
# Removed uuid as it's built into Python

# Development and Testing
pytest==7.4.3
pytest-flask==1.3.0
pytest-cov==4.1.0
black==23.12.0
flake8==6.1.0

# Logging and Monitoring
structlog==23.2.0
