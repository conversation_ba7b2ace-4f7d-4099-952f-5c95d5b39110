2025-08-06 23:22:35,381 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5001
2025-08-06 23:22:35,382 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-06 23:22:42,726 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:22:42] "[33mGET / HTTP/1.1[0m" 404 -
2025-08-06 23:22:42,842 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:22:42] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-06 23:23:43,442 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:23:43] "[33mGET /api/v1 HTTP/1.1[0m" 404 -
2025-08-06 23:23:49,952 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:23:49] "[33mGET /api/v1/ HTTP/1.1[0m" 404 -
2025-08-06 23:24:02,885 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:24:02] "[33mGET /api/v1/ HTTP/1.1[0m" 404 -
2025-08-06 23:24:03,894 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:24:03] "[33mGET /api/v1/ HTTP/1.1[0m" 404 -
2025-08-06 23:25:45,941 - app.utils.decorators - ERROR - Unexpected error: 'AuthResource' object is not subscriptable
2025-08-06 23:25:45,943 - app - ERROR - Exception on /api/v1/auth/register [POST]
Traceback (most recent call last):
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask/app.py", line 867, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask/app.py", line 852, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask_restful/__init__.py", line 493, in wrapper
    return self.make_response(data, code, headers=headers)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask_restful/__init__.py", line 522, in make_response
    resp = self.representations[mediatype](data, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask_restful/representations/json.py", line 21, in output_json
    dumped = dumps(data, **settings) + "\n"
             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type Response is not JSON serializable
2025-08-06 23:25:45,949 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:25:45] "[35m[1mPOST /api/v1/auth/register HTTP/1.1[0m" 500 -
2025-08-06 23:26:59,639 - app.utils.decorators - ERROR - Unexpected error: 'AuthResource' object is not subscriptable
2025-08-06 23:26:59,640 - app - ERROR - Exception on /api/v1/auth/register [POST]
Traceback (most recent call last):
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask/app.py", line 867, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask/app.py", line 852, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask_restful/__init__.py", line 493, in wrapper
    return self.make_response(data, code, headers=headers)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask_restful/__init__.py", line 522, in make_response
    resp = self.representations[mediatype](data, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/git-repo/algo_trader/venv/lib/python3.11/site-packages/flask_restful/representations/json.py", line 21, in output_json
    dumped = dumps(data, **settings) + "\n"
             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type Response is not JSON serializable
2025-08-06 23:26:59,643 - werkzeug - INFO - 127.0.0.1 - - [06/Aug/2025 23:26:59] "[35m[1mPOST /api/v1/auth/register HTTP/1.1[0m" 500 -
